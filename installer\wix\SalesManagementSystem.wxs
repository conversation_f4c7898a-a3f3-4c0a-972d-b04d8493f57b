<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">

  <!-- Product Definition -->
  <Product Id="*"
           Name="Sales Management System"
           Language="1033"
           Version="2.1.0"
           Manufacturer="Hamza Damra"
           UpgradeCode="B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E">

    <!-- Package Information -->
    <Package InstallerVersion="200"
             Compressed="yes"
             InstallScope="perMachine"
             Description="Professional Sales Management System with Enhanced Update Functionality"
             Comments="Professional Sales Management System with Enhanced Update Functionality"
             Manufacturer="Hamza Damra" />

    <!-- Media Definition -->
    <MediaTemplate EmbedCab="yes" />

    <!-- Upgrade Logic -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of Sales Management System is already installed." />

    <!-- Features -->
    <Feature Id="ProductFeature" Title="Sales Management System" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
      <ComponentRef Id="RegistryEntries" />
    </Feature>

    <!-- UI Configuration -->
    <UI>
      <UIRef Id="WixUI_InstallDir" />
      <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="InstallDirDlg" Order="2">1</Publish>
      <Publish Dialog="InstallDirDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg" Order="2">1</Publish>
    </UI>

    <!-- License Agreement -->
    <WixVariable Id="WixUILicenseRtf" Value="installer\LICENSE.rtf" />

    <!-- Installation Directory -->
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />

  </Product>

  <!-- Directory Structure -->
  <Fragment>
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="Sales Management System" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Sales Management System" />
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>
  </Fragment>

  <!-- Components -->
  <Fragment>
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <!-- Main Application Files -->
      <Component Id="MainExecutable" Guid="*">
        <File Id="SalesManagementSystemExe" 
              Source="$(var.SourceDir)\SalesManagementSystem.exe" 
              KeyPath="yes" />
      </Component>
      
      <!-- Runtime Dependencies -->
      <Component Id="RuntimeFiles" Guid="*">
        <File Id="RuntimeDll" 
              Source="$(var.SourceDir)\runtime\*" 
              KeyPath="yes" />
      </Component>
    </ComponentGroup>

    <!-- Desktop Shortcut -->
    <Component Id="ApplicationShortcut" Directory="DesktopFolder" Guid="*">
      <Shortcut Id="ApplicationDesktopShortcut"
                Name="Sales Management System"
                Description="Professional Sales Management System"
                Target="[#SalesManagementSystemExe]"
                WorkingDirectory="INSTALLFOLDER"
                Icon="AppIcon.exe" />
      <RemoveFolder Id="DesktopFolder" On="uninstall" />
      <RegistryValue Root="HKCU"
                     Key="Software\HamzaDamra\SalesManagementSystem"
                     Name="installed"
                     Type="integer"
                     Value="1"
                     KeyPath="yes" />
    </Component>

    <!-- Registry Entries -->
    <Component Id="RegistryEntries" Directory="INSTALLFOLDER" Guid="*">
      <!-- Application Registration -->
      <RegistryKey Root="HKLM" Key="SOFTWARE\HamzaDamra\SalesManagementSystem">
        <RegistryValue Name="InstallPath" Type="string" Value="[INSTALLFOLDER]" />
        <RegistryValue Name="Version" Type="string" Value="2.1.0" />
        <RegistryValue Name="DisplayName" Type="string" Value="Sales Management System" />
        <RegistryValue Name="Publisher" Type="string" Value="Hamza Damra" />
        <RegistryValue Name="InstallDate" Type="string" Value="[Date]" />
        <RegistryValue Name="BackendURL" Type="string" Value="https://sales-managment-system-backend-springboot.onrender.com" />
        <RegistryValue Name="FullscreenMode" Type="integer" Value="1" />
      </RegistryKey>

      <!-- Uninstall Information -->
      <RegistryKey Root="HKLM" Key="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}">
        <RegistryValue Name="DisplayName" Type="string" Value="Sales Management System" />
        <RegistryValue Name="DisplayVersion" Type="string" Value="2.1.0" />
        <RegistryValue Name="Publisher" Type="string" Value="Hamza Damra" />
        <RegistryValue Name="InstallLocation" Type="string" Value="[INSTALLFOLDER]" />
        <RegistryValue Name="UninstallString" Type="string" Value="msiexec /x {B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}" />
        <RegistryValue Name="NoModify" Type="integer" Value="1" />
        <RegistryValue Name="NoRepair" Type="integer" Value="1" />
        <RegistryValue Name="EstimatedSize" Type="integer" Value="150000" />
      </RegistryKey>

      <!-- File Association (optional) -->
      <RegistryKey Root="HKLM" Key="SOFTWARE\Classes\.sms">
        <RegistryValue Type="string" Value="SalesManagementSystem.Document" />
      </RegistryKey>
      
      <RegistryKey Root="HKLM" Key="SOFTWARE\Classes\SalesManagementSystem.Document">
        <RegistryValue Type="string" Value="Sales Management System Document" />
        <RegistryKey Key="DefaultIcon">
          <RegistryValue Type="string" Value="[#SalesManagementSystemExe],0" />
        </RegistryKey>
        <RegistryKey Key="shell\open\command">
          <RegistryValue Type="string" Value="&quot;[#SalesManagementSystemExe]&quot; &quot;%1&quot;" />
        </RegistryKey>
      </RegistryKey>

      <RemoveFolder Id="INSTALLFOLDER" On="uninstall" />
      <RegistryValue Root="HKCU" 
                     Key="Software\HamzaDamra\SalesManagementSystem" 
                     Name="installed" 
                     Type="integer" 
                     Value="1" 
                     KeyPath="yes" />
    </Component>
  </Fragment>

  <!-- Application Icon -->
  <Icon Id="AppIcon.exe" SourceFile="installer\icons\app-icon.ico" />

</Wix>
